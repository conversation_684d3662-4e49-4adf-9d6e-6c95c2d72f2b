'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { WordBlastEngineProps } from '../WordBlastEngine';
import { WordObject, ParticleEffect, BaseThemeEngine } from '../BaseThemeEngine';

// Updated PirateShip interface
interface PirateShip extends WordObject {
  shipType: 'galleon' | 'frigate' | 'sloop';
  sailsUp: boolean;
  sinkingProgress: number;
  size: number;
  direction: 'left' | 'right';
  targetY: number;
  spawnTime: number;
  feedbackStatus?: 'incorrect';
  firing?: boolean;
}

// Cannon ball interface
interface CannonBall {
  id: string;
  x: number;
  y: number;
  targetX: number;
  targetY: number;
  progress: number;
}

// Theme engine class
class PirateAdventureThemeEngine extends BaseThemeEngine {
  // Inherits all base functionality
}

const MAX_ACTIVE_SHIPS = 15; // Reduced to prevent overcrowding
const WATER_LINE = 300; // Start ships higher up to use more screen space
const MIN_SHIP_DISTANCE = 220; // Minimum distance between ships

export default function PirateAdventureEngine(props: WordBlastEngineProps) {
  const {
    currentChallenge,
    challenges,
    onCorrectAnswer,
    onIncorrectAnswer,
    onChallengeComplete,
    isPaused,
    gameActive,
    difficulty,
    playSFX
  } = props;

  const [pirateShips, setPirateShips] = useState<PirateShip[]>([]);
  const [particles, setParticles] = useState<ParticleEffect[]>([]);
  const [wordsCollected, setWordsCollected] = useState<string[]>([]);
  const [challengeProgress, setChallengeProgress] = useState(0);
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [cannonBalls, setCannonBalls] = useState<CannonBall[]>([]);

  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const themeEngine = useRef(new PirateAdventureThemeEngine());

  // Helper function to check if Y position conflicts with existing ships
  const isYPositionValid = (targetY: number, existingShips: PirateShip[]) => {
    return !existingShips.some(ship => {
      const yDistance = Math.abs(targetY - ship.targetY);
      return yDistance < MIN_SHIP_DISTANCE;
    });
  };

  // Helper function to create a single new ship object with side-to-side movement
  const createSingleShip = (existingShips: PirateShip[]) => {
    if (!currentChallenge) return null;

    const correctWords = currentChallenge.words;
    const decoys = themeEngine.current.generateDecoys(correctWords, challenges, difficulty);

    // Ensure the correct word for the current challenge is always an option
    const allWords = [...decoys];
    const expectedWord = currentChallenge.words[currentWordIndex];
    if (!allWords.includes(expectedWord)) {
      allWords.push(expectedWord);
    }

    const word = allWords[Math.floor(Math.random() * allWords.length)];

    const screenWidth = typeof window !== 'undefined' ? window.innerWidth : 1200;
    const screenHeight = typeof window !== 'undefined' ? window.innerHeight : 800;
    const shipTypes: ('galleon' | 'frigate' | 'sloop')[] = ['galleon', 'frigate', 'sloop'];

    // Determine direction and starting position (side-to-side movement)
    const direction: 'left' | 'right' = Math.random() > 0.5 ? 'left' : 'right';
    const startX = direction === 'left' ? -250 : screenWidth + 250;

    // Create distinct vertical zones to prevent clustering
    // Use more of the screen - start from below the UI elements and go to near bottom
    const minY = WATER_LINE; // Start right after the UI elements
    const maxY = screenHeight - 80; // Leave some margin at bottom
    const usableHeight = maxY - minY;
    const zoneHeight = usableHeight / MAX_ACTIVE_SHIPS;
    const zoneIndex = existingShips.length % MAX_ACTIVE_SHIPS;
    const baseY = minY + (zoneIndex * zoneHeight);

    // Add some randomness within the zone
    const targetY = baseY + Math.random() * (zoneHeight * 0.8);

    return {
      id: `ship-${Date.now()}-${Math.random()}`,
      word,
      isCorrect: correctWords.includes(word),
      x: startX,
      y: targetY,
      speed: 0.5 + Math.random() * 0.8,
      rotation: (Math.random() - 0.5) * 3,
      scale: 0.8 + Math.random() * 0.3,
      spawnTime: Date.now(),
      clicked: false,
      shipType: shipTypes[Math.floor(Math.random() * shipTypes.length)],
      sailsUp: Math.random() > 0.2,
      sinkingProgress: 0,
      size: 0.9 + Math.random() * 0.2,
      feedbackStatus: undefined,
      direction,
      targetY
    };
  };

  // Spawn initial set of pirate ships when challenge changes or unpauses
  const spawnInitialPirateShips = () => {
    if (!currentChallenge) return;

    // Clear existing ships first
    setPirateShips([]);

    // Create ships with staggered timing to prevent all spawning at once
    const createShipsWithDelay = (count: number, delay: number = 0) => {
      if (count <= 0) return;
      
      setTimeout(() => {
        setPirateShips(currentShips => {
          const newShip = createSingleShip(currentShips);
          if (newShip) {
            return [...currentShips, newShip];
          }
          return currentShips;
        });
        
        // Create next ship with delay
        if (count > 1) {
          createShipsWithDelay(count - 1, 1000);
        }
      }, delay);
    };

    // Start creating ships with staggered timing
    createShipsWithDelay(Math.min(3, MAX_ACTIVE_SHIPS), 100);
  };

  // Update ship positions and sailing motion
  const updateShips = () => {
    if (isPaused || !gameActive) return;

    const screenWidth = typeof window !== 'undefined' ? window.innerWidth : 1200;
    const shipsToRemove: PirateShip[] = [];

    setPirateShips(prev =>
      prev.map(ship => {
        let newX = ship.x;
        // Move horizontally based on direction at slower speed
        if (ship.direction === 'left') {
          newX = ship.x + ship.speed;
        } else {
          newX = ship.x - ship.speed;
        }

        // Check if ship has moved off screen
        const offScreen = (ship.direction === 'left' && newX > screenWidth + 300) ||
          (ship.direction === 'right' && newX < -300);

        if (offScreen) {
          shipsToRemove.push(ship);
          return { ...ship, x: newX };
        }

        // Apply a smooth sine wave to the Y position for a floating effect (gentler)
        const newY = ship.targetY + Math.sin((Date.now() - ship.spawnTime) * 0.002 + ship.x / 150) * 15;

        return {
          ...ship,
          x: newX,
          y: newY,
          rotation: ship.rotation + Math.sin(Date.now() * 0.002) * 0.3
        };
      }).filter(ship => {
        const shouldRemove = shipsToRemove.some(s => s.id === ship.id);
        if (shouldRemove) {
          // Trigger a trail particle effect as the ship leaves
          const trailParticles = themeEngine.current.createParticleEffect(ship.x, ship.y, 'ambient', 10);
          setParticles(currentParticles => [...currentParticles, ...trailParticles]);
        }
        return !shouldRemove;
      })
    );

    // Regenerate new ships if below threshold (with better spacing)
    const remainingShips = pirateShips.length - shipsToRemove.length;
    if (remainingShips < MAX_ACTIVE_SHIPS && remainingShips >= 0) {
      setTimeout(() => {
        setPirateShips(currentShips => {
          const filteredShips = currentShips.filter(ship => !shipsToRemove.some(s => s.id === ship.id));
          if (filteredShips.length < MAX_ACTIVE_SHIPS) {
            const newShip = createSingleShip(filteredShips);
            if (newShip) {
              return [...filteredShips, newShip];
            }
          }
          return filteredShips;
        });
      }, 2000 + Math.random() * 3000); // Longer delay to prevent overcrowding
    }
  };

  // Update cannon balls
  const updateCannonBalls = () => {
    setCannonBalls((prev: CannonBall[]) =>
      prev.map((ball: CannonBall) => ({
        ...ball,
        progress: Math.min(ball.progress + 0.05, 1),
        x: ball.x + (ball.targetX - ball.x) * 0.05,
        y: ball.y + (ball.targetY - ball.y) * 0.05
      })).filter((ball: CannonBall) => ball.progress < 1)
    );
  };

  // Realistic Ship Component
  const ShipComponent = ({ ship }: { ship: PirateShip }) => {
    const shipSize = ship.shipType === 'galleon' ? 1.2 : ship.shipType === 'frigate' ? 1.0 : 0.8;
    const isFlipped = ship.direction === 'right';

    return (
      <div className={`relative ${isFlipped ? 'scale-x-[-1]' : ''}`} style={{ transform: `scale(${shipSize})` }}>
        {/* Ship Hull - Main Body */}
        <div className="relative">
          {/* Lower Hull */}
          <div className="relative bg-gradient-to-b from-black via-gray-900 to-gray-950 rounded-b-full border-2 border-gray-800 shadow-2xl"
            style={{ width: '140px', height: '45px' }}>

            {/* Hull Details */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-32 h-3 bg-amber-950 rounded-b-full"></div>

            {/* Ship Planks */}
            <div className="absolute top-2 left-2 right-2 h-0.5 bg-amber-700 opacity-60"></div>
            <div className="absolute top-4 left-2 right-2 h-0.5 bg-amber-700 opacity-60"></div>
            <div className="absolute top-6 left-2 right-2 h-0.5 bg-amber-700 opacity-60"></div>
          </div>

          {/* Upper Deck */}
          <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-28 h-4 bg-gradient-to-b from-gray-800 to-black rounded-t-lg border border-gray-700"></div>

          {/* Captain's Quarters (for galleon) */}
          {ship.shipType === 'galleon' && (
            <div className="absolute -top-4 right-4 w-8 h-6 bg-gradient-to-b from-gray-700 to-black rounded-t border border-gray-600"></div>
          )}

          {/* Masts */}
          <div className="absolute top-0 left-1/3 transform -translate-x-1/2 -translate-y-16 w-1 h-20 bg-black shadow-sm"></div>
          {ship.shipType !== 'sloop' && (
            <div className="absolute top-0 right-1/3 transform translate-x-1/2 -translate-y-14 w-1 h-16 bg-black shadow-sm"></div>
          )}

          {/* Main Sails */}
          {ship.sailsUp && (
            <>
              <div className="absolute top-0 left-1/3 transform -translate-x-1/2 -translate-y-14 w-12 h-14 bg-gradient-to-br from-gray-100 via-gray-200 to-gray-300 rounded border border-gray-400 shadow-lg">
                {/* Sail Details */}
                <div className="absolute top-2 left-1 right-1 h-0.5 bg-gray-400 opacity-40"></div>
                <div className="absolute top-4 left-1 right-1 h-0.5 bg-gray-400 opacity-40"></div>
                <div className="absolute top-6 left-1 right-1 h-0.5 bg-gray-400 opacity-40"></div>
              </div>

              {ship.shipType !== 'sloop' && (
                <div className="absolute top-0 right-1/3 transform translate-x-1/2 -translate-y-12 w-10 h-12 bg-gradient-to-br from-gray-100 via-gray-200 to-gray-300 rounded border border-gray-400 shadow-lg">
                  <div className="absolute top-2 left-1 right-1 h-0.5 bg-gray-400 opacity-40"></div>
                  <div className="absolute top-4 left-1 right-1 h-0.5 bg-gray-400 opacity-40"></div>
                </div>
              )}
            </>
          )}

          {/* Cannons */}
          <div className="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-2 w-6 h-2 bg-gray-800 rounded-full shadow-md">
            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-2 h-3 bg-gray-700 rounded-full"></div>
          </div>

          {ship.shipType === 'galleon' && (
            <div className="absolute top-1/2 left-0 transform -translate-y-1/2 translate-y-2 -translate-x-2 w-5 h-2 bg-gray-800 rounded-full shadow-md">
              <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-gray-700 rounded-full"></div>
            </div>
          )}

          {/* Ship Flag */}
          <div className="absolute top-0 right-6 transform -translate-y-12 w-4 h-3 bg-red-600 border-l-2 border-amber-900 shadow-sm">
            <div className="w-full h-full bg-gradient-to-r from-red-600 to-red-700"></div>
          </div>

          {/* Ship Details */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 translate-y-1">
            {/* Anchor */}
            <div className="absolute -left-16 top-2 w-2 h-3 bg-gray-600 rounded-sm opacity-80"></div>

            {/* Ship Wheel */}
            <div className="absolute -right-12 -top-2 w-3 h-3 border-2 border-gray-600 rounded-full bg-gray-800"></div>
          </div>

          {/* Ship Wake Effects */}
          <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-20 h-3 bg-white/20 rounded-full blur-sm"></div>
          <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-16 h-2 bg-white/30 rounded-full blur-xs"></div>
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-white/40 rounded-full"></div>
        </div>

        {/* Word Display - Always readable, not flipped */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 translate-y-1 bg-amber-50 text-amber-900 px-3 py-1.5 rounded-lg border-2 border-amber-600 font-bold text-sm shadow-xl">
          {ship.word}
        </div>

        {/* Feedback Effects */}
        {ship.feedbackStatus === 'incorrect' && (
          <div className="absolute inset-0 bg-red-500/30 rounded-full animate-pulse"></div>
        )}

        {ship.clicked && (
          <div className="absolute inset-0 bg-green-500/30 rounded-full animate-ping"></div>
        )}
      </div>
    );
  };

  // Handle ship click (cannon fire)
  const handleShipClick = (ship: PirateShip) => {
    console.log('Ship clicked:', ship.word, 'Expected:', currentChallenge.words[currentWordIndex]);
    if (ship.feedbackStatus === 'incorrect' || ship.clicked) return;

    const expectedWord = currentChallenge.words[currentWordIndex];
    const isClickValidAndInOrder = ship.isCorrect && (ship.word === expectedWord);

    if (isClickValidAndInOrder) {
      setPirateShips(prev =>
        prev.map(s => s.id === ship.id ? { ...s, clicked: true } : s)
      );

      const newWordsCollected = [...wordsCollected, ship.word];
      setWordsCollected(newWordsCollected);
      setCurrentWordIndex(prev => prev + 1);

      const successParticles = themeEngine.current.createParticleEffect(
        ship.x, ship.y, 'success', 20
      );
      setParticles(prev => [...prev, ...successParticles]);

      playSFX('gem');

      const sequenceBonus = currentWordIndex * 2;
      onCorrectAnswer(10 + sequenceBonus + (difficulty === 'advanced' ? 5 : 0));

      if (newWordsCollected.length === currentChallenge.words.length) {
        setTimeout(() => {
          onChallengeComplete();
          setWordsCollected([]);
          setChallengeProgress(0);
          setCurrentWordIndex(0);
        }, 500);
      } else {
        setChallengeProgress(newWordsCollected.length / currentChallenge.words.length);
      }

      setTimeout(() => {
        setPirateShips(prev => prev.filter(s => s.id !== ship.id));
      }, 300);

    } else if (ship.isCorrect && !isClickValidAndInOrder) {
      setPirateShips(prev =>
        prev.map(s => s.id === ship.id ? { ...s, feedbackStatus: 'incorrect' } : s)
      );

      const warningParticles = themeEngine.current.createParticleEffect(
        ship.x, ship.y, 'ambient', 12
      );
      setParticles(prev => [...prev, ...warningParticles]);

      playSFX('wrong-answer');
      onCorrectAnswer(-2);

      setTimeout(() => {
        setPirateShips(prev =>
          prev.map(s => s.id === ship.id ? { ...s, feedbackStatus: undefined } : s)
        );
      }, 500);

    } else {
      setPirateShips(prev =>
        prev.map(s => s.id === ship.id ? { ...s, feedbackStatus: 'incorrect' } : s)
      );

      const errorParticles = themeEngine.current.createParticleEffect(
        ship.x, ship.y, 'error', 12
      );
      setParticles(prev => [...prev, ...errorParticles]);

      playSFX('wrong-answer');
      onIncorrectAnswer();

      setTimeout(() => {
        setPirateShips(prev =>
          prev.map(s => s.id === ship.id ? { ...s, feedbackStatus: undefined } : s)
        );
      }, 500);
    }
  };

  const updateParticles = () => {
    setParticles(prev =>
      prev.map(p => ({
        ...p,
        x: p.x + p.vx,
        y: p.y + p.vy,
        life: p.life - 0.02
      })).filter(p => p.life > 0)
    );
  };

  useEffect(() => {
    const animate = () => {
      updateShips();
      updateCannonBalls();
      updateParticles();
      animationRef.current = requestAnimationFrame(animate);
    };

    if (gameActive) {
      animationRef.current = requestAnimationFrame(animate);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [gameActive, isPaused, pirateShips.length]);

  useEffect(() => {
    if (currentChallenge && gameActive) {
      setWordsCollected([]);
      setChallengeProgress(0);
      setCurrentWordIndex(0);
      spawnInitialPirateShips();
    }
  }, [currentChallenge, gameActive]);

  // Add back ship spawning logic to maintain ship count
  useEffect(() => {
    if (
      currentChallenge &&
      gameActive &&
      !isPaused &&
      pirateShips.length < MAX_ACTIVE_SHIPS
    ) {
      const numToSpawn = Math.min(1, MAX_ACTIVE_SHIPS - pirateShips.length);
      if (numToSpawn > 0) {
        setTimeout(() => {
          setPirateShips(currentShips => {
            if (currentShips.length < MAX_ACTIVE_SHIPS) {
              const newShip = createSingleShip(currentShips);
              if (newShip) {
                return [...currentShips, newShip];
              }
            }
            return currentShips;
          });
        }, Math.random() * 3000 + 2000); // Random delay between 2-5 seconds
      }
    }
  }, [isPaused, gameActive, currentChallenge, pirateShips.length]);

  if (!currentChallenge) {
    return (
      <div className="flex items-center justify-center h-full w-full bg-slate-900 text-blue-200 text-xl">
        No challenge loaded. Please start a game or check your data source.
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 overflow-hidden"
    >
      {/* Video Background */}
      <div className="absolute inset-0">
        <video
          className="absolute inset-0 w-full h-full object-cover"
          autoPlay
          muted
          loop
          playsInline
        >
          <source src="/games/noughts-and-crosses/images/pirate-adventure/pirate-adventure-bg.mp4" type="video/mp4" />
        </video>

        {/* Video overlay gradient for better readability */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-transparent to-black/40"></div>
      </div>

      {/* English Sentence Display */}
      <div className="absolute top-40 left-1/2 transform -translate-x-1/2 z-50">
        <div className="bg-black/70 backdrop-blur-sm rounded-lg px-6 py-4 border border-amber-500/30">
          <div className="text-center">
            <div className="text-sm text-amber-300 mb-1">🏴‍☠️ Decode this treasure map:</div>
            <div className="text-xl font-bold text-white">{currentChallenge.english}</div>
            <div className="text-sm text-blue-300 mt-2">
              Click the {currentChallenge.targetLanguage} pirate ships in the correct order
            </div>
          </div>
        </div>
      </div>

      {/* Progress Display */}
      <div className="absolute top-24 left-1/2 transform -translate-x-1/2 z-50">
        <div className="bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2 border border-amber-500/20">
          <div className="text-center">
            <div className="text-sm text-amber-300">Treasure Map:</div>
            <div className="text-lg font-bold text-white">
              {wordsCollected.join(' ')}
            </div>
          </div>
        </div>
      </div>

      {/* Pirate Ships */}
      <AnimatePresence>
        {pirateShips.map((ship) => (
          <motion.div
            key={ship.id}
            initial={{ opacity: 0, scale: 0.5, x: ship.x, y: ship.y }}
            animate={{
              opacity: 1,
              scale: ship.size,
              x: ship.x,
              y: ship.y,
            }}
            exit={{ opacity: 0, scale: 0 }}
            onClick={() => handleShipClick(ship)}
            className="absolute cursor-pointer transition-all duration-300 hover:scale-105 select-none"
            style={{
              transform: `translate(-50%, -50%) rotate(${ship.rotation}deg)`,
              zIndex: 10,
              pointerEvents: 'auto'
            }}
          >
            <ShipComponent ship={ship} />
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Particles */}
      <AnimatePresence>
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            initial={{ opacity: 1, scale: 1 }}
            animate={{
              opacity: particle.life,
              scale: 1 - (1 - particle.life) * 0.5,
              x: particle.x,
              y: particle.y
            }}
            exit={{ opacity: 0, scale: 0 }}
            className="absolute pointer-events-none"
            style={{
              width: particle.size,
              height: particle.size,
              backgroundColor: particle.color,
              borderRadius: '50%',
              zIndex: 5
            }}
          />
        ))}
      </AnimatePresence>
    </div>
  );
}